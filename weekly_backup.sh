#!/bin/bash

# Monthly Database Backup Script
# Combines fetch_database_list and monthly_backup_dynamic GitLab CI jobs
# 
# This script:
# 1. Fetches production database list from DigitalOcean API
# 2. Backs up each database using pg_dump
# 3. Cleans up old backups
#
# Usage: ./monthly_backup.sh
# 
# Required environment variables:
# - DO_API_TOKEN: DigitalOcean API token
# - POSTGRES_PASSWORD_PRODUCTION: PostgreSQL password for production database
#
# Optional environment variables:
# - BACKUP_BASE_DIR: Base directory for backups (default: /opt/backups)
# - BACKUP_RETENTION_DAYS: Days to keep backups (default: 72)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_BASE_DIR="${BACKUP_BASE_DIR:-/opt/backups}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-72}"
DATABASE_LIST_FILE="${SCRIPT_DIR}/database_list.txt"
LOG_FILE="${SCRIPT_DIR}/backup_$(date +%Y%m%d_%H%M%S).log"

# Database connection details
DB_HOST="db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com"
DB_PORT="25060"
DB_USER="doadmin"

# DigitalOcean API configuration
DO_API_URL="https://api.digitalocean.com/v2"
DO_API_TOKEN="***********************************************************************"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_error() {
    log "${RED}ERROR: $1${NC}"
}

log_success() {
    log "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    log "${YELLOW}WARNING: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("curl" "jq" "pg_dump")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command '$cmd' not found. Please install it."
            exit 1
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Fetch database list from DigitalOcean API
fetch_database_list() {
    log "Fetching database list from DigitalOcean API..."
    
    # Clear previous database list
    > "$DATABASE_LIST_FILE"
    
    # Fetch database clusters
    local response
    response=$(curl -s -H "Content-Type: application/json" \
                   -H "Authorization: Bearer $DO_API_TOKEN" \
                   "$DO_API_URL/databases")
    
    if [[ $? -ne 0 ]]; then
        log_error "Failed to fetch database clusters from DigitalOcean API"
        return 1
    fi
    
    # Parse and filter production databases
    local clusters
    clusters=$(echo "$response" | jq -r '.databases[] | select(.engine == "pg" and .status == "online" and (.name | contains("test") | not) and (.name | contains("clone") | not)) | .id + ":" + .name')
    
    if [[ -z "$clusters" ]]; then
        log_warning "No production database clusters found"
        return 0
    fi
    
    # Fetch individual databases from each cluster
    while IFS=':' read -r cluster_id cluster_name; do
        log "Fetching databases from cluster: $cluster_name ($cluster_id)"
        
        local db_response
        db_response=$(curl -s -H "Content-Type: application/json" \
                          -H "Authorization: Bearer $DO_API_TOKEN" \
                          "$DO_API_URL/databases/$cluster_id/dbs")
        
        if [[ $? -eq 0 ]]; then
            echo "# Databases from cluster: $cluster_name ($cluster_id)" >> "$DATABASE_LIST_FILE"
            echo "$db_response" | jq -r '.dbs[].name' >> "$DATABASE_LIST_FILE"
        else
            log_warning "Could not fetch databases from cluster $cluster_id"
        fi
    done <<< "$clusters"
    
    log "Found databases:"
    cat "$DATABASE_LIST_FILE" | tee -a "$LOG_FILE"
    log_success "Database list fetched successfully"
}

# Backup databases
backup_databases() {
    log "Starting database backup process..."
    
    if [[ ! -f "$DATABASE_LIST_FILE" ]]; then
        log_error "Database list file not found: $DATABASE_LIST_FILE"
        return 1
    fi
    
    # Create backup directory with timestamp
    local backup_dir="$BACKUP_BASE_DIR/$(date +%Y-%m-%d)"
    log "Creating backup directory: $backup_dir"
    mkdir -p "$backup_dir"
    
    # Initialize counters
    local backup_count=0
    local failed_count=0
    
    # Set PostgreSQL password
    export PGPASSWORD="AVNS_R_6HWi4QOV3P8Eq_SSW"
    
    # Read database list and backup each one
    while IFS= read -r db_name; do
        # Skip comments and empty lines
        if [[ "$db_name" =~ ^#.*$ ]] || [[ -z "$db_name" ]]; then
            continue
        fi
        
        log "Backing up database: $db_name"
        
        # Create backup directly in the target directory
        local backup_file="$backup_dir/${db_name}.pgsql"
        
        if pg_dump -h "$DB_HOST" \
                   -U "$DB_USER" \
                   -p "$DB_PORT" \
                   -Fc "$db_name" > "$backup_file"; then
            log_success "Successfully backed up $db_name"
            backup_count=$((backup_count + 1))
        else
            log_error "Failed to backup $db_name"
            failed_count=$((failed_count + 1))
            # Remove failed backup file if it exists
            [[ -f "$backup_file" ]] && rm -f "$backup_file"
        fi
    done < "$DATABASE_LIST_FILE"
    
    # Unset password
    unset PGPASSWORD
    
    log ""
    log "=== Backup Summary ==="
    log "Successful backups: $backup_count"
    log "Failed backups: $failed_count"
    log "Files in backup directory:"
    ls -la "$backup_dir/" | tee -a "$LOG_FILE"
    
    # Return error if any backups failed
    if [[ $failed_count -gt 0 ]]; then
        log_error "Some backups failed. Check logs above."
        return 1
    fi
    
    log_success "All backups completed successfully"
    return 0
}

# Clean up old backups
cleanup_old_backups() {
    log ""
    log "=== Cleaning up old backups ==="
    log "Removing backup directories older than $BACKUP_RETENTION_DAYS days..."
    
    if [[ -d "$BACKUP_BASE_DIR" ]]; then
        find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} \; -print | tee -a "$LOG_FILE"
        
        log ""
        log "=== Current backup directories ==="
        ls -la "$BACKUP_BASE_DIR/" | tee -a "$LOG_FILE"
    else
        log_warning "Backup base directory does not exist: $BACKUP_BASE_DIR"
    fi
}

# Main execution
main() {
    log "Starting monthly backup script..."
    log "Log file: $LOG_FILE"
    
    check_prerequisites
    fetch_database_list
    backup_databases
    cleanup_old_backups
    
    log_success "Monthly backup completed successfully!"
}

# Handle script interruption
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
