#!/usr/bin/env ruby

require 'uri'
require 'net/http'
require 'json'
require 'optparse'

# token: ***********************************************************************
# cluster ids - 
#    test: d97d1e1b-f417-40b5-b985-4031b30cd436
#    prod: dc20acf7-b2e4-49ca-be47-83e9bcd9d40b
# get IDs of all databases
# ruby do_db_fork.rb --token *********************************************************************** --command list
# ruby do_db_fork.rb --token *********************************************************************** --command fork --database-id d97d1e1b-f417-40b5-b985-4031b30cd436 --fork-name "db-postgresql-test-clone"

# ruby do_db_fork.rb --token *********************************************************************** --command fork --database-id dc20acf7-b2e4-49ca-be47-83e9bcd9d40b --fork-name "db-postgresql-test-clone"

# example curl
# curl --location --request PUT 'https://trove-staging.imtins.com/api/v1/staging/test/Banana' \
# --header 'Content-Type: application/json' \
# --header 'Authorization: Bearer RCyp2vxqViitqYyk5gmi8sxdiNpMBJ' \
# --data '{"value":"Carter"}';

# GET greenmask % curl --location --request GET 'https://trove-staging.imtins.com/api/v1/staging/test/Banana' \
# --header 'Content-Type: application/json' \
# --header 'Authorization: Bearer RCyp2vxqViitqYyk5gmi8sxdiNpMBJ'

class DigitalOceanDatabaseFork
  API_URL = 'https://api.digitalocean.com/v2'

  # UUIDs for trusted sources
  IMT_PRODUCTION_UUID = 'd32cc3de-e8e5-45b4-aa34-6c7835e16793'
  IMT_TEST_UUID = '5744a3fc-68f2-49d5-be56-1d41a35e4945'

  def initialize(api_token)
    @api_token = api_token
    @headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{@api_token}"
    }
  end
  
  def list_databases
    response = make_request(
      :get,
      "#{API_URL}/databases"
    )
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['databases']
      puts "Available databases:"
      databases.each do |db|
        puts "ID: #{db['id']} - Name: #{db['name']} - Engine: #{db['engine']} - Status: #{db['status']}"
      end
    else
      handle_error(response)
    end
  end
  
  def get_clone_connection_host
    response = make_request(:get, "#{API_URL}/databases")
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['databases']
      clone_db = databases.find { |db| db['tags']&.include?('clone') }
      if clone_db
        return {
          host: clone_db.dig('private_connection', 'host'),
          id: clone_db['id']
        }
      end
    end
    nil
  end

  def fork_database(db_id, fork_name, options = {})
    # Get clone info before starting fork
    clone_info = get_clone_connection_host
    puts "Found clone database host: #{clone_info[:host]}" if clone_info

    # First get the source database info to use as defaults
    source_db = get_database_info(db_id, quiet: true)
    
    if source_db.nil?
      puts "❌ Cannot fork: Unable to retrieve source database information"
      return
    end
    
    puts "Forking database #{db_id} (#{source_db['name']}) to new database named #{fork_name}..."
    
    # Build payload with required fields
    payload = {
      name: fork_name,
      engine: options[:engine] || source_db['engine'],
      version: options[:version] || source_db['version'],
      region: options[:region] || source_db['region'],
      size: options[:size] || source_db['size'],
      num_nodes: options[:num_nodes] || source_db['num_nodes'] || 1,
      tags: options[:tags] || ["clone"],
      private_network_uuid: options[:private_network_uuid] || source_db['private_network_uuid']
    }
    
    # Always set cluster_name explicitly
    payload[:cluster_name] = options[:cluster_name] || "#{fork_name}-cluster"
    
    # Add backup restore information with database_name matching cluster name
    payload[:backup_restore] = {
      database_id: db_id,
      # Use the source database name by default, or override if specified
      database_name: options[:database_name] || source_db['name']
    }
    
    # Add backup timestamp if provided
    if options[:backup_time]
      payload[:backup_restore][:backup_created_at] = options[:backup_time]
    end
    
    # Clean up nil values
    payload.delete_if { |_, v| v.nil? }
    
    puts "Creating fork with the following configuration:"
    puts JSON.pretty_generate(payload)
    
    # Debug output
    puts "\nSending to endpoint: #{API_URL}/databases"
    
    response = make_request(
      :post,
      "#{API_URL}/databases",
      payload.to_json
    )
    
    if response.is_a?(Net::HTTPSuccess)
      result = JSON.parse(response.body)['database']
      private_connection_host = result.dig('private_connection', 'host')
      puts "Private connection host: #{private_connection_host}"
      puts "✅ Fork initiated successfully!"
      puts "New database ID: #{result['id']}"
      puts "Status: #{result['status']}"
      puts "Connection string will be available when database is ready."

      # Wait for the cluster to become ready
      wait_for_cluster_ready(result['id'], clone_info: clone_info)
    else
      handle_error(response)
      puts "\nDebug info:"
      puts "Request payload: #{payload.to_json}"
    end
  end
  
  def get_database_backups(db_id)
    response = make_request(
      :get,
      "#{API_URL}/databases/#{db_id}/backups"
    )
    
    if response.is_a?(Net::HTTPSuccess)
      backups = JSON.parse(response.body)['backups']
      puts "Available backups for database #{db_id}:"
      backups.each do |backup|
        puts "Created at: #{backup['created_at']} - Size: #{backup['size_gigabytes']}GB"
      end
      backups
    else
      handle_error(response)
      []
    end
  end
  
  def get_database_info(db_id, quiet: false)
    response = make_request(
      :get,
      "#{API_URL}/databases/#{db_id}"
    )
    
    if response.is_a?(Net::HTTPSuccess)
      db = JSON.parse(response.body)['database']
      unless quiet
        puts "Database information:"
        puts "ID: #{db['id']}"
        puts "Name: #{db['name']}"
        puts "Engine: #{db['engine']}"
        puts "Version: #{db['version']}"
        puts "Status: #{db['status']}"
        puts "Region: #{db['region']}"
        puts "Size: #{db['size']}"
        puts "Number of nodes: #{db['num_nodes'] || 1}"
        puts "Private network UUID: #{db['private_network_uuid']}" if db['private_network_uuid']
      end
      db
    else
      handle_error(response)
      nil
    end
  end
  
  def list_regions
    response = make_request(
      :get,
      "#{API_URL}/regions"
    )
    
    if response.is_a?(Net::HTTPSuccess)
      regions = JSON.parse(response.body)['regions']
      puts "Available regions:"
      regions.each do |region|
        puts "Slug: #{region['slug']} - Name: #{region['name']} - Available: #{region['available']}"
      end
    else
      handle_error(response)
    end
  end
  
  def list_database_sizes
    puts "Common database sizes:"
    puts "db-s-1vcpu-1gb - 1vCPU, 1GB RAM"
    puts "db-s-1vcpu-2gb - 1vCPU, 2GB RAM"
    puts "db-s-2vcpu-4gb - 2vCPU, 4GB RAM"
    puts "db-s-4vcpu-8gb - 4vCPU, 8GB RAM"
    puts "db-s-8vcpu-16gb - 8vCPU, 16GB RAM"
    puts "db-s-16vcpu-32gb - 16vCPU, 32GB RAM"
    puts "Check the DigitalOcean documentation for more sizes."
  end

  def wait_for_cluster_ready(db_id, clone_info: nil, timeout: 7200, interval: 100)
    start_time = Time.now
    puts "Waiting for cluster #{db_id} to become ready..."

    loop do
      db_info = get_database_info(db_id, quiet: true)
      if db_info.nil?
        puts "❌ Failed to retrieve database information. Exiting."
        return false
      end

      status = db_info['status']
      puts "Current status: #{status}"

      if status == 'online'
        puts "✅ Cluster #{db_id} is now online!"

        # Update firewall rules for the new clone database
        puts "Updating firewall rules for clone database..."
        begin
          update_database_firewall_rules(db_id)
          puts "✅ Firewall rules updated successfully"
        rescue => e
          puts "⚠️  Warning: Failed to update firewall rules: #{e.message}"
          puts "You may need to update trusted sources manually"
        end

        if clone_info
          new_host = db_info.dig('private_connection', 'host')
          puts "Updating clone host from #{clone_info[:host]} to #{new_host}..."

          # Update Kubernetes Service instead of API call
          begin
            update_kubernetes_service(clone_info[:host], new_host)
            puts "Successfully updated Kubernetes service, deleting old clone database..."
            delete_database(clone_info[:id])
          rescue => e
            puts "Failed to update Kubernetes service: #{e.message}"
          end
        else
          puts "No clone database found, skipping update step"
        end

        return true
      elsif Time.now - start_time > timeout
        puts "❌ Timeout reached. Cluster #{db_id} is not ready."
        return false
      end

      sleep(interval)
    end
  end

  def delete_database(db_id)
    puts "Deleting database #{db_id}..."
    response = make_request(:delete, "#{API_URL}/databases/#{db_id}")
    
    if response.is_a?(Net::HTTPSuccess)
      puts "✅ Database #{db_id} deleted successfully"
      true
    else
      handle_error(response)
      false
    end
  end
  
  private
  
  def make_request(method, url, body = nil)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    
    request = case method
      when :get
        Net::HTTP::Get.new(uri.request_uri, @headers)
      when :post
        post = Net::HTTP::Post.new(uri.request_uri, @headers)
        post.body = body if body
        post
      when :delete
        Net::HTTP::Delete.new(uri.request_uri, @headers)
      else
        raise "Unsupported HTTP method: #{method}"
    end
    
    http.request(request)
  end
  
  def update_database_firewall_rules(db_id)
    puts "Updating trusted sources for database #{db_id}..."

    # Get current firewall rules
    current_rules = get_database_firewall_rules(db_id)
    if current_rules.nil?
      raise "Failed to retrieve current firewall rules"
    end

    puts "Current trusted sources: #{current_rules.map { |rule| rule['value'] }.join(', ')}"

    # Prepare updated rules
    updated_rules = current_rules.dup

    # Remove "imt-production" UUID if it exists
    production_rule = updated_rules.find { |rule| rule['value'] == IMT_PRODUCTION_UUID }
    if production_rule
      puts "Removing trusted source: imt-production (#{IMT_PRODUCTION_UUID})"
      updated_rules.delete(production_rule)
    else
      puts "imt-production UUID not found in current trusted sources"
    end

    # Add "imt-test" UUID if it doesn't exist
    test_rule = updated_rules.find { |rule| rule['value'] == IMT_TEST_UUID }
    if test_rule.nil?
      puts "Adding trusted source: imt-test (#{IMT_TEST_UUID})"
      # Use 'k8s' type for Kubernetes cluster UUIDs (based on the UUID format)
      # If there was a production rule, use the same type, otherwise default to 'k8s'
      rule_type = production_rule ? production_rule['type'] : 'k8s'
      updated_rules << {
        'type' => rule_type,
        'value' => IMT_TEST_UUID
      }
    else
      puts "imt-test UUID already exists in trusted sources"
    end

    # Update firewall rules if changes were made
    if updated_rules != current_rules
      puts "Applying firewall rule changes..."
      update_database_firewall_rules_api(db_id, updated_rules)
      puts "Updated trusted sources: #{updated_rules.map { |rule| rule['value'] }.join(', ')}"
    else
      puts "No firewall rule changes needed"
    end
  end

  def get_database_firewall_rules(db_id)
    puts "Fetching current firewall rules for database #{db_id}..."
    response = make_request(:get, "#{API_URL}/databases/#{db_id}/firewall")

    if response.is_a?(Net::HTTPSuccess)
      firewall_data = JSON.parse(response.body)
      rules = firewall_data['rules'] || []
      puts "Found #{rules.length} firewall rules"
      return rules
    else
      puts "❌ Failed to fetch firewall rules"
      handle_error(response)
      return nil
    end
  end

  def update_database_firewall_rules_api(db_id, rules)
    puts "Updating firewall rules via API..."
    puts "Endpoint: #{API_URL}/databases/#{db_id}/firewall"

    # Clean the rules - remove API-generated fields and keep only type and value
    clean_rules = rules.map do |rule|
      {
        'type' => rule['type'],
        'value' => rule['value']
      }
    end

    payload = {
      rules: clean_rules
    }

    puts "Payload: #{payload.to_json}"

    # Try POST method first (seems to be the correct method based on error)
    response = make_request(:post, "#{API_URL}/databases/#{db_id}/firewall", payload.to_json)

    if response.is_a?(Net::HTTPSuccess)
      puts "✅ Firewall rules updated successfully with POST"
      return true
    elsif response.code == '405' # Method Not Allowed
      puts "POST method not allowed, trying PUT..."
      response = make_request(:put, "#{API_URL}/databases/#{db_id}/firewall", payload.to_json)

      if response.is_a?(Net::HTTPSuccess)
        puts "✅ Firewall rules updated successfully with PUT"
        return true
      else
        puts "❌ Failed to update firewall rules with PUT"
        handle_error(response)
        raise "API request failed with status #{response.code}"
      end
    else
      puts "❌ Failed to update firewall rules with POST"
      handle_error(response)
      raise "API request failed with status #{response.code}"
    end
  end

  def update_kubernetes_service(old_host, new_host)
    puts "Updating Kubernetes Service db-clone-url in namespace clone..."
    puts "Old host: #{old_host}"
    puts "New host: #{new_host}"

    # Create kubectl patch command to update the externalName
    patch_json = {
      spec: {
        externalName: new_host
      }
    }.to_json

    # Execute kubectl patch command
    kubectl_cmd = "kubectl patch service db-clone-url -n clone --type='merge' -p '#{patch_json}'"
    puts "Executing: #{kubectl_cmd}"

    result = `#{kubectl_cmd} 2>&1`
    exit_status = $?.exitstatus

    if exit_status == 0
      puts "✅ Successfully updated Kubernetes service"
      puts "Service db-clone-url now points to: #{new_host}"

      # Verify the update
      verify_cmd = "kubectl get service db-clone-url -n clone -o jsonpath='{.spec.externalName}'"
      current_host = `#{verify_cmd}`.strip
      puts "Verified current externalName: #{current_host}"

      if current_host == new_host
        puts "✅ Update verified successfully"
      else
        puts "⚠️  Warning: Verification shows different host: #{current_host}"
      end
    else
      puts "❌ Failed to update Kubernetes service"
      puts "Error output: #{result}"
      raise "kubectl patch failed with exit status #{exit_status}"
    end
  end

  def handle_error(response)
    puts "❌ Error: #{response.code} - #{response.message}"
    begin
      error_details = JSON.parse(response.body)
      error_message = error_details['message'] || error_details['error']
      if error_details.dig('errors', 'additional')
        error_message += ": " + error_details['errors']['additional'].join(", ")
      end
      puts "Details: #{error_message}"
    rescue
      puts "Response body: #{response.body}"
    end
  end
end

# CLI options parsing
options = {
  # Set defaults
  token: '***********************************************************************',
  db_id: 'dc20acf7-b2e4-49ca-be47-83e9bcd9d40b',
  fork_name: 'db-postgresql-clone'
}

# Set up command line argument parser
opt_parser = OptionParser.new do |opts|
  opts.banner = "Usage: #{$PROGRAM_NAME} [options]"
  
  opts.on("-t", "--token TOKEN", "DigitalOcean API token (optional)") do |token|
    options[:token] = token
  end
  
  opts.on("-c", "--command COMMAND", "Command: list, fork, info, backups, regions, sizes") do |cmd|
    options[:command] = cmd
  end
  
  opts.on("-d", "--database-id ID", "Database ID to fork or get info for (optional)") do |id|
    options[:db_id] = id
  end
  
  opts.on("-n", "--fork-name NAME", "Name for the new forked database (optional)") do |name|
    options[:fork_name] = name
  end
  
  opts.on("--database-name NAME", "Name of the actual database (default: same as source database)") do |name|
    options[:database_name] = name
  end
  
  opts.on("-r", "--region REGION", "Region for the new database (default: same as source)") do |region|
    options[:region] = region
  end
  
  opts.on("-s", "--size SIZE", "Size of the new database (default: same as source)") do |size|
    options[:size] = size
  end
  
  opts.on("--engine ENGINE", "Database engine (default: same as source)") do |engine|
    options[:engine] = engine
  end
  
  opts.on("--version VERSION", "Engine version (default: same as source)") do |version|
    options[:version] = version
  end
  
  opts.on("--num-nodes NODES", Integer, "Number of database nodes (default: same as source)") do |nodes|
    options[:num_nodes] = nodes
  end
  
  # Make cluster_name prominent in the help text
  opts.on("--cluster-name NAME", "Cluster name (REQUIRED, default: [fork-name]-cluster)") do |name|
    options[:cluster_name] = name
  end
  
  opts.on("--private-network-uuid UUID", "Private network UUID") do |uuid|
    options[:private_network_uuid] = uuid
  end
  
  opts.on("-b", "--backup-time TIME", "Specific backup timestamp to restore from") do |time|
    options[:backup_time] = time
  end
  
  opts.on("--tags x,y,z", Array, "Tags to apply to the forked database") do |tags|
    options[:tags] = tags
  end
  
  opts.on("-h", "--help", "Show this help message") do
    puts opts
    exit
  end
end

begin
  opt_parser.parse!
  
  # Check for required options
  if options[:command].nil?
    puts "Error: Command is required"
    puts opt_parser
    exit 1
  end
  
  # Initialize client
  client = DigitalOceanDatabaseFork.new(options[:token])
  
  # Execute requested command
  case options[:command]
  when "list"
    client.list_databases
  when "fork"
    # Add timestamp to fork name
    timestamp = Time.now.strftime('%Y%m%d-%H%M')
    fork_name_with_timestamp = "#{options[:fork_name]}-#{timestamp}"
    client.fork_database(options[:db_id], fork_name_with_timestamp, options)
  when "info"
    client.get_database_info(options[:db_id])
  when "backups"
    client.get_database_backups(options[:db_id])
  when "regions"
    client.list_regions
  when "sizes"
    client.list_database_sizes
  else
    puts "Error: Unknown command '#{options[:command]}'"
    puts opt_parser
    exit 1
  end
  
rescue OptionParser::InvalidOption, OptionParser::MissingArgument
  puts $!.to_s
  puts opt_parser
  exit 1
end