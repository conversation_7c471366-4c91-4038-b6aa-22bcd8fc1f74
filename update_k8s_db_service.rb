#!/usr/bin/env ruby

require 'json'
require 'optparse'

class KubernetesServiceUpdater
  def initialize
    @service_name = 'db-clone-url'
    @namespace = 'clone'
  end

  def update_service(new_host)
    puts "Updating Kubernetes Service #{@service_name} in namespace #{@namespace}..."
    puts "New host: #{new_host}"
    
    # Get current externalName for comparison
    current_host = get_current_external_name
    if current_host
      puts "Current host: #{current_host}"
      
      if current_host == new_host
        puts "✅ Service already points to the specified host. No update needed."
        return true
      end
    end
    
    # Create kubectl patch command to update the externalName
    patch_json = {
      spec: {
        externalName: new_host
      }
    }.to_json
    
    # Execute kubectl patch command
    kubectl_cmd = "kubectl patch service #{@service_name} -n #{@namespace} --type='merge' -p '#{patch_json}'"
    puts "Executing: #{kubectl_cmd}"
    
    result = `#{kubectl_cmd} 2>&1`
    exit_status = $?.exitstatus
    
    if exit_status == 0
      puts "✅ Successfully updated Kubernetes service"
      puts "Service #{@service_name} now points to: #{new_host}"
      
      # Verify the update
      verify_update(new_host)
      return true
    else
      puts "❌ Failed to update Kubernetes service"
      puts "Error output: #{result}"
      return false
    end
  end

  def get_current_external_name
    verify_cmd = "kubectl get service #{@service_name} -n #{@namespace} -o jsonpath='{.spec.externalName}' 2>/dev/null"
    current_host = `#{verify_cmd}`.strip
    
    if $?.exitstatus == 0 && !current_host.empty?
      return current_host
    else
      puts "⚠️  Could not retrieve current externalName (service may not exist)"
      return nil
    end
  end

  def verify_update(expected_host)
    puts "Verifying update..."
    current_host = get_current_external_name
    
    if current_host == expected_host
      puts "✅ Update verified successfully"
      puts "Current externalName: #{current_host}"
    else
      puts "⚠️  Warning: Verification shows different host: #{current_host}"
      puts "Expected: #{expected_host}"
    end
  end

  def show_current_service
    puts "Current service configuration:"
    show_cmd = "kubectl get service #{@service_name} -n #{@namespace} -o yaml"
    result = `#{show_cmd} 2>&1`
    
    if $?.exitstatus == 0
      puts result
    else
      puts "❌ Failed to retrieve service configuration"
      puts "Error: #{result}"
    end
  end

  def list_clone_databases
    puts "Fetching available clone databases from DigitalOcean..."
    
    # This would require the DigitalOcean API token
    # For now, just show the command that could be used
    puts "To list available clone databases, run:"
    puts "ruby do_db_fork.rb --command list"
  end
end

# CLI options parsing
options = {}

opt_parser = OptionParser.new do |opts|
  opts.banner = "Usage: #{$PROGRAM_NAME} [options]"
  
  opts.on("-u", "--update HOST", "Update service to point to new host") do |host|
    options[:update_host] = host
  end
  
  opts.on("-s", "--show", "Show current service configuration") do
    options[:show] = true
  end
  
  opts.on("-l", "--list", "List available clone databases") do
    options[:list] = true
  end
  
  opts.on("-h", "--help", "Show this help message") do
    puts opts
    exit
  end
end

begin
  opt_parser.parse!
rescue OptionParser::InvalidOption => e
  puts "Error: #{e.message}"
  puts opt_parser
  exit 1
end

# Create updater instance
updater = KubernetesServiceUpdater.new

# Execute based on options
if options[:update_host]
  success = updater.update_service(options[:update_host])
  exit(success ? 0 : 1)
elsif options[:show]
  updater.show_current_service
elsif options[:list]
  updater.list_clone_databases
else
  puts "Error: No action specified"
  puts opt_parser
  exit 1
end
