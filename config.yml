common:
  pg_bin_path: "/usr/lib/postgresql/16/bin"
  tmp_dir: "/tmp"

storage:
  type: "directory"
  directory:
    path: /builds/networkservices/greenmask/dumps

validate:
#  resolved_warnings:
#    - "aa808fb574a1359c6606e464833feceb"

dump:
  pg_dump_options: # pg_dump option that will be provided
    # dbname: "host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com port=25060 user=doadmin password=password dbname=cms-staging"
    jobs: 10

  transformation: # List of tables to transform
    - schema: "public" # Table schema
      name: "imt_auth_user"  # Table name
      transformers: # List of transformers to apply
        - name: "RandomEmail"
          params:
            column: "email"
            keep_null: false
        - name: "RandomUsername"
          params:
            column: "username"
            keep_null: false




restore:
  pg_restore_options: # pg_restore option (you can use the same options as pg_restore has)
    jobs: 10
    # dbname: "host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com port=25060 user=doadmin password=password dbname=cms-clone"
  scripts:
    post-data:
      - name: "post-data after script [1]"
        when: "after"
        query: "update imt_auth_user set password='pbkdf2_sha256$260000$bSCsOPkdo8B7LoqfiBa53a$eCIr/jtABhZ2l5IQfPsRG3KgkldTRb8JtBDEZMU/dGI=' where username in ('imt.online', 'imt.print', 'imt.platform')"