common:
  pg_bin_path: "/usr/lib/postgresql/16/bin"
  tmp_dir: "/tmp"

storage:
  type: "directory"
  directory:
    path: /builds/networkservices/greenmask/dumps

validate:
#  resolved_warnings:
#    - "aa808fb574a1359c6606e464833feceb"

dump:
  pg_dump_options: # pg_dump option that will be provided
    # dbname: "host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com port=25060 user=doadmin password=password dbname=cms-staging"
    jobs: 10





restore:
  pg_restore_options: # pg_restore option (you can use the same options as pg_restore has)
    jobs: 10
    # dbname: "host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com port=25060 user=doadmin password=password dbname=cms-clone"
