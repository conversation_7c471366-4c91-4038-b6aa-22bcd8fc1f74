#!/usr/bin/env ruby

require 'uri'
require 'net/http'
require 'json'
require 'optparse'

class DatabaseFirewallManager
  API_URL = "https://api.digitalocean.com/v2"
  
  def initialize(token)
    @token = token
  end

  def list_databases
    puts "Fetching database list..."
    response = make_request(:get, "#{API_URL}/databases")
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['databases']
      
      puts "\nAvailable databases:"
      puts "=" * 50
      databases.each do |db|
        puts "ID: #{db['id']}"
        puts "Name: #{db['name']}"
        puts "Engine: #{db['engine']}"
        puts "Status: #{db['status']}"
        puts "Tags: #{db['tags']&.join(', ') || 'None'}"
        puts "-" * 30
      end
    else
      handle_error(response)
    end
  end

  def show_firewall_rules(db_id)
    puts "Fetching firewall rules for database #{db_id}..."
    response = make_request(:get, "#{API_URL}/databases/#{db_id}/firewall")
    
    if response.is_a?(Net::HTTPSuccess)
      firewall_data = JSON.parse(response.body)
      rules = firewall_data['rules'] || []
      
      puts "\nCurrent firewall rules:"
      puts "=" * 40
      if rules.empty?
        puts "No firewall rules configured"
      else
        rules.each_with_index do |rule, index|
          puts "#{index + 1}. Type: #{rule['type']}, Value: #{rule['value']}"
        end
      end
      puts "Total rules: #{rules.length}"
    else
      handle_error(response)
    end
  end

  def add_trusted_source(db_id, source_name, source_type = 'tag')
    puts "Adding trusted source '#{source_name}' to database #{db_id}..."
    
    # Get current rules
    current_rules = get_current_rules(db_id)
    return false if current_rules.nil?
    
    # Check if source already exists
    existing_rule = current_rules.find { |rule| rule['value'] == source_name }
    if existing_rule
      puts "✅ Trusted source '#{source_name}' already exists"
      return true
    end
    
    # Add new rule
    updated_rules = current_rules + [{
      'type' => source_type,
      'value' => source_name
    }]
    
    update_firewall_rules(db_id, updated_rules)
  end

  def remove_trusted_source(db_id, source_name)
    puts "Removing trusted source '#{source_name}' from database #{db_id}..."
    
    # Get current rules
    current_rules = get_current_rules(db_id)
    return false if current_rules.nil?
    
    # Find and remove the rule
    rule_to_remove = current_rules.find { |rule| rule['value'] == source_name }
    if rule_to_remove.nil?
      puts "⚠️  Trusted source '#{source_name}' not found"
      return true
    end
    
    updated_rules = current_rules.reject { |rule| rule['value'] == source_name }
    update_firewall_rules(db_id, updated_rules)
  end

  def update_clone_firewall_rules(db_id)
    puts "Updating firewall rules for clone database #{db_id}..."
    puts "- Adding: imt-test"
    puts "- Removing: imt-production"
    
    # Get current rules
    current_rules = get_current_rules(db_id)
    return false if current_rules.nil?
    
    updated_rules = current_rules.dup
    
    # Remove imt-production
    updated_rules.reject! { |rule| rule['value'] == 'imt-production' }
    
    # Add imt-test if not exists
    unless updated_rules.any? { |rule| rule['value'] == 'imt-test' }
      rule_type = current_rules.first&.dig('type') || 'tag'
      updated_rules << {
        'type' => rule_type,
        'value' => 'imt-test'
      }
    end
    
    update_firewall_rules(db_id, updated_rules)
  end

  private

  def get_current_rules(db_id)
    response = make_request(:get, "#{API_URL}/databases/#{db_id}/firewall")
    
    if response.is_a?(Net::HTTPSuccess)
      firewall_data = JSON.parse(response.body)
      return firewall_data['rules'] || []
    else
      handle_error(response)
      return nil
    end
  end

  def update_firewall_rules(db_id, rules)
    payload = { rules: rules }
    response = make_request(:put, "#{API_URL}/databases/#{db_id}/firewall", payload.to_json)
    
    if response.is_a?(Net::HTTPSuccess)
      puts "✅ Firewall rules updated successfully"
      puts "New trusted sources: #{rules.map { |rule| rule['value'] }.join(', ')}"
      return true
    else
      puts "❌ Failed to update firewall rules"
      handle_error(response)
      return false
    end
  end

  def make_request(method, url, body = nil)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_PEER

    case method
    when :get
      request = Net::HTTP::Get.new(uri)
    when :post
      request = Net::HTTP::Post.new(uri)
    when :put
      request = Net::HTTP::Put.new(uri)
    when :delete
      request = Net::HTTP::Delete.new(uri)
    end

    request['Content-Type'] = 'application/json'
    request['Authorization'] = "Bearer #{@token}"
    request.body = body if body

    http.request(request)
  end

  def handle_error(response)
    puts "❌ Error: #{response.code} - #{response.message}"
    begin
      error_details = JSON.parse(response.body)
      error_message = error_details['message'] || error_details['error']
      puts "Details: #{error_message}"
    rescue
      puts "Response body: #{response.body}"
    end
  end
end

# CLI options parsing
options = {
  token: '***********************************************************************'
}

opt_parser = OptionParser.new do |opts|
  opts.banner = "Usage: #{$PROGRAM_NAME} [options]"
  
  opts.on("-t", "--token TOKEN", "DigitalOcean API token") do |token|
    options[:token] = token
  end
  
  opts.on("-d", "--database-id ID", "Database ID") do |db_id|
    options[:database_id] = db_id
  end
  
  opts.on("-l", "--list", "List all databases") do
    options[:list] = true
  end
  
  opts.on("-s", "--show", "Show firewall rules for database") do
    options[:show] = true
  end
  
  opts.on("-a", "--add SOURCE", "Add trusted source") do |source|
    options[:add_source] = source
  end
  
  opts.on("-r", "--remove SOURCE", "Remove trusted source") do |source|
    options[:remove_source] = source
  end
  
  opts.on("-c", "--clone-rules", "Apply clone database firewall rules (add imt-test, remove imt-production)") do
    options[:clone_rules] = true
  end
  
  opts.on("-h", "--help", "Show this help message") do
    puts opts
    exit
  end
end

begin
  opt_parser.parse!
rescue OptionParser::InvalidOption => e
  puts "Error: #{e.message}"
  puts opt_parser
  exit 1
end

# Create manager instance
manager = DatabaseFirewallManager.new(options[:token])

# Execute based on options
if options[:list]
  manager.list_databases
elsif options[:show]
  if options[:database_id]
    manager.show_firewall_rules(options[:database_id])
  else
    puts "Error: Database ID required for --show"
    exit 1
  end
elsif options[:add_source]
  if options[:database_id]
    success = manager.add_trusted_source(options[:database_id], options[:add_source])
    exit(success ? 0 : 1)
  else
    puts "Error: Database ID required for --add"
    exit 1
  end
elsif options[:remove_source]
  if options[:database_id]
    success = manager.remove_trusted_source(options[:database_id], options[:remove_source])
    exit(success ? 0 : 1)
  else
    puts "Error: Database ID required for --remove"
    exit 1
  end
elsif options[:clone_rules]
  if options[:database_id]
    success = manager.update_clone_firewall_rules(options[:database_id])
    exit(success ? 0 : 1)
  else
    puts "Error: Database ID required for --clone-rules"
    exit 1
  end
else
  puts "Error: No action specified"
  puts opt_parser
  exit 1
end
