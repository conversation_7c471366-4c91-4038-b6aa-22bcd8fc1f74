image: docker:latest
services:
  - docker:dind

stages:
  - backup
  - restore
     
# Job to fetch database list from DigitalOcean API
fetch_database_list:
  stage: backup
  image:
    name: ruby:3.1-alpine
  before_script:
    - apk add --no-cache curl
    - chmod +x ./fetch_do_databases.rb
  script:
    - ./fetch_do_databases.rb --command production > database_list.txt
    - echo "Found databases:"
    - cat database_list.txt
    - |
      # Convert database list to GitLab CI matrix format
      echo "databases:" > databases.yml
      while IFS= read -r db_name; do
        # Skip comments and empty lines
        if [[ ! "$db_name" =~ ^#.*$ ]] && [[ -n "$db_name" ]]; then
          echo "  - \"$db_name\"" >> databases.yml
        fi
      done < database_list.txt
    - echo "Generated matrix:"
    - cat databases.yml
  artifacts:
    paths:
      - database_list.txt
      - databases.yml
    expire_in: 1 hour
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - when: manual
  tags:
    - d<PERSON><PERSON>

# Dynamic monthly backup with integrated SFTP transfer
monthly_backup_dynamic:
  stage: backup
  image:
    name: postgres:alpine
  needs:
    - fetch_database_list
  script:
    - |
      # Create backup directory with timestamp
      BACKUP_DIR="/opt/backups/$(date +%Y-%m-%d)"
      echo "Creating backup directory: $BACKUP_DIR"
      mkdir -p "$BACKUP_DIR"

      # Read database list and backup each one
      backup_count=0
      failed_count=0

      while IFS= read -r db_name; do
        # Skip comments and empty lines
        if [[ ! "$db_name" =~ ^#.*$ ]] && [[ -n "$db_name" ]]; then
          echo "Backing up database: $db_name"

          # Create backup directly in the target directory
          PGPASSWORD=$POSTGRES_PASSWORD_PRODUCTION pg_dump \
            -h db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com \
            -U doadmin \
            -p 25060 \
            -Fc "$db_name" > "$BACKUP_DIR/${db_name}.pgsql"

          if [ $? -eq 0 ]; then
            echo "✅ Successfully backed up $db_name"
            backup_count=$((backup_count + 1))
          else
            echo "❌ Failed to backup $db_name"
            failed_count=$((failed_count + 1))
          fi
        fi
      done < database_list.txt

      echo ""
      echo "=== Backup Summary ==="
      echo "Successful backups: $backup_count"
      echo "Failed backups: $failed_count"
      echo "Files in backup directory:"
      ls -la "$BACKUP_DIR/"

      # Clean up old backups (older than 72 days)
      echo ""
      echo "=== Cleaning up old backups ==="
      echo "Removing backup directories older than 72 days..."
      find /opt/backups -maxdepth 1 -type d -mtime +72 -exec rm -rf {} \; -print

      echo ""
      echo "=== Current backup directories ==="
      ls -la /opt/backups/

      # Exit with error if any backups failed
      if [ $failed_count -gt 0 ]; then
        echo "❌ Some backups failed. Check logs above."
        exit 1
      fi

      echo "✅ All backups completed successfully and transferred to $BACKUP_DIR"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - when: manual
  tags:
    - dbbackup


messup_database:
  image:
    name: postgres:alpine
  stage: restore
  variables:
    SOURCE_HOST: ""
    DEST_HOST: ""
    DATABASE_NAME: ""
  script:
    - chmod +x ./messup.sh
    - |
      if [ -n "$SOURCE_HOST" ] && [ -n "$DEST_HOST" ] && [ -n "$DATABASE_NAME" ]; then
        ./messup.sh -s "$SOURCE_HOST" -d "$DEST_HOST" -n "$DATABASE_NAME"
      elif [ -n "$DATABASE_NAME" ]; then
        ./messup.sh -n "$DATABASE_NAME"
      else
        echo "No parameters provided, using defaults from db_list.txt"
        ./messup.sh
      fi
  rules:
    - when: manual
  tags:
    - dmitri