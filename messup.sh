#!/bin/bash

# Configuration
SOURCE_HOST="db-postgresql-test-mar-23-backup-do-user-2355736-0.i.db.ondigitalocean.com"    # Source database host
SOURCE_PORT="25060"         # Source database port
SOURCE_USER="doadmin"  # Source database username
SOURCE_PASS="AVNS_Iu2uSxDCIEnSoN8f0rr"  # Source database password

DEST_HOST="db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com"      # Destination database host
DEST_PORT="25060"           # Destination database port
DEST_USER="doadmin"      # Destination database username
DEST_PASS="AVNS_Iu2uSxDCIEnSoN8f0rr"      # Destination database password

DB_LIST_FILE="db_list.txt" # File containing the list of databases, one per line
DUMP_DIR="./dumps"         # Directory to store dump files
# RESTORE_DB="restore_target" # Name of the target database for restoration

SINGLE_DB=""

# Parse command line arguments
while getopts "s:d:n:" opt; do
  case $opt in
    s) SOURCE_HOST="$OPTARG" ;;
    d) DEST_HOST="$OPTARG" ;;
    n) SINGLE_DB="$OPTARG" ;;
    \?) echo "Invalid option -$OPTARG" >&2
        echo "Usage: $0 [-s source_host] [-d dest_host] [-n database_name]" >&2
        exit 1 ;;
  esac
done



# Export passwords to avoid interactive prompts
export PGPASSWORD=$SOURCE_PASS

# Ensure dump directory exists
mkdir -p "$DUMP_DIR"

process_database() {
    local DB_NAME=$1
    echo "Processing database: $DB_NAME"

    # Dump database
    DUMP_FILE="$DUMP_DIR/$DB_NAME.sql"
    echo "Dumping database $DB_NAME to $DUMP_FILE..."
    pg_dump -h "$SOURCE_HOST" -p "$SOURCE_PORT" -U "$SOURCE_USER" -F c -b -v -f "$DUMP_FILE" "$DB_NAME"

    if [[ $? -ne 0 ]]; then
        echo "Error dumping database $DB_NAME. Skipping..."
        return 1
    fi

    # Restore database
    echo "Restoring $DB_NAME to $DB_NAME on destination..."
    export PGPASSWORD=$DEST_PASS
    pg_restore -h "$DEST_HOST" -p "$DEST_PORT" -U "$DEST_USER" -d "$DB_NAME" -c -v "$DUMP_FILE"

    if [[ $? -ne 0 ]]; then
        echo "Error restoring database $DB_NAME. Skipping..."
        return 1
    fi
}

# Process either single database or list from file
if [[ -n "$SINGLE_DB" ]]; then
    process_database "$SINGLE_DB"
else
    if [[ ! -f "$DB_LIST_FILE" ]]; then
        echo "Error: $DB_LIST_FILE not found and no database specified with -n"
        exit 1
    fi
    
    while IFS= read -r DB_NAME; do
        if [[ -n "$DB_NAME" ]]; then
            process_database "$DB_NAME"
        fi
    done < "$DB_LIST_FILE"
fi

# Cleanup
unset PGPASSWORD
echo "All done!"