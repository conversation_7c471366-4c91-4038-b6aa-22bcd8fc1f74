#!/usr/bin/env ruby

require 'uri'
require 'net/http'
require 'json'
require 'optparse'

class DigitalOceanDatabaseList
  API_URL = 'https://api.digitalocean.com/v2'
  
  def initialize(api_token)
    @api_token = api_token
    @headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{@api_token}"
    }
  end
  
  def get_production_databases
    response = make_request(:get, "#{API_URL}/databases")
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['databases']
      
      # Filter for production databases (you may need to adjust this filter)
      production_dbs = databases.select do |db|
        db['engine'] == 'pg' && 
        db['status'] == 'online' &&
        !db['name'].include?('test') &&
        !db['name'].include?('clone')
      end
      
      # Get individual database names from the production cluster
      production_dbs.each do |cluster|
        cluster_id = cluster['id']
        db_names = get_database_names_from_cluster(cluster_id)
        
        if db_names.any?
          puts "# Databases from cluster: #{cluster['name']} (#{cluster_id})"
          db_names.each { |name| puts name }
        end
      end
    else
      handle_error(response)
      exit 1
    end
  end
  
  def get_database_names_from_cluster(cluster_id)
    response = make_request(:get, "#{API_URL}/databases/#{cluster_id}/dbs")
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['dbs']
      return databases.map { |db| db['name'] }
    else
      STDERR.puts "Warning: Could not fetch databases from cluster #{cluster_id}"
      return []
    end
  end
  
  def list_all_databases_with_details
    response = make_request(:get, "#{API_URL}/databases")
    
    if response.is_a?(Net::HTTPSuccess)
      databases = JSON.parse(response.body)['databases']
      
      puts "All DigitalOcean Database Clusters:"
      databases.each do |db|
        puts "Cluster: #{db['name']} (ID: #{db['id']})"
        puts "  Engine: #{db['engine']}"
        puts "  Status: #{db['status']}"
        puts "  Region: #{db['region']}"
        puts "  Host: #{db.dig('connection', 'host')}"
        
        # Get individual databases in this cluster
        db_names = get_database_names_from_cluster(db['id'])
        if db_names.any?
          puts "  Databases: #{db_names.join(', ')}"
        end
        puts ""
      end
    else
      handle_error(response)
    end
  end
  
  private
  
  def make_request(method, url, body = nil)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    
    request = case method
      when :get
        Net::HTTP::Get.new(uri.request_uri, @headers)
      when :post
        post = Net::HTTP::Post.new(uri.request_uri, @headers)
        post.body = body if body
        post
      else
        raise "Unsupported HTTP method: #{method}"
    end
    
    http.request(request)
  end
  
  def handle_error(response)
    STDERR.puts "❌ Error: #{response.code} - #{response.message}"
    begin
      error_details = JSON.parse(response.body)
      error_message = error_details['message'] || error_details['error']
      STDERR.puts "Details: #{error_message}"
    rescue
      STDERR.puts "Response body: #{response.body}"
    end
  end
end

# CLI options parsing
options = {
  token: ENV['DO_API_TOKEN'] || '***********************************************************************'
}

opt_parser = OptionParser.new do |opts|
  opts.banner = "Usage: #{$PROGRAM_NAME} [options]"
  
  opts.on("-t", "--token TOKEN", "DigitalOcean API token") do |token|
    options[:token] = token
  end
  
  opts.on("-c", "--command COMMAND", "Command: production, all") do |cmd|
    options[:command] = cmd
  end
  
  opts.on("-h", "--help", "Show this help message") do
    puts opts
    exit
  end
end

begin
  opt_parser.parse!
  
  # Default command
  options[:command] ||= 'production'
  
  # Initialize client
  client = DigitalOceanDatabaseList.new(options[:token])
  
  # Execute requested command
  case options[:command]
  when "production"
    client.get_production_databases
  when "all"
    client.list_all_databases_with_details
  else
    puts "Error: Unknown command '#{options[:command]}'"
    puts opt_parser
    exit 1
  end
  
rescue OptionParser::InvalidOption, OptionParser::MissingArgument
  puts $!.to_s
  puts opt_parser
  exit 1
end
