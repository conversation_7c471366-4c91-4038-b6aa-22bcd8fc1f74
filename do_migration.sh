#!/bin/bash

# ssh root@*************
# apt install -y postgresql-client
# scp -r greenmask/ root@*************:/root/

# Configuration
SOURCE_HOST="localhost"    # Source database host
SOURCE_PORT="5432"         # Source database port
SOURCE_USER="postgres"  # Source database username
SOURCE_PASS="Hangover9-Plunging-Phoniness-Cameo"  # Source database password
SSH_USER="carter_minear"             # SSH username for tunnel
SSH_PORT="22"          # SSH port
LOCAL_PORT="15432"     # Local port for SSH tunnel

DEST_HOST="db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com"      # Destination database host
DEST_PORT="25060"           # Destination database port
DEST_USER="doadmin"      # Destination database username
DEST_PASS="AVNS_R_6HWi4QOV3P8Eq_SSW"      # Destination database password

DB_LIST_FILE="db_list.txt" # File containing the list of databases, one per line
DUMP_DIR="./dumps"         # Directory to store dump files
# RESTORE_DB="restore_target" # Name of the target database for restoration

SINGLE_DB=""
OPERATION=""

# Parse command line arguments
while getopts "s:d:n:u:o:" opt; do
  case $opt in
    s) SOURCE_HOST="$OPTARG" ;;
    d) DEST_HOST="$OPTARG" ;;
    n) SINGLE_DB="$OPTARG" ;;
    u) SSH_USER="$OPTARG" ;;
    o) OPERATION="$OPTARG" ;;
    \?) echo "Invalid option -$OPTARG" >&2
        echo "Usage: $0 [-s source_host] [-d dest_host] [-n database_name] [-u ssh_user] [-o backup|restore]" >&2
        exit 1 ;;
  esac
done

backup_database() {
    local DB_NAME=$1
    echo "Backing up database: $DB_NAME"
    DUMP_FILE="$DUMP_DIR/$DB_NAME.sql"
    echo "Dumping database $DB_NAME to $DUMP_FILE..."
    pg_dump -h "$SOURCE_HOST" -p "$SOURCE_PORT" -U "$SOURCE_USER" -F c -b -v -f "$DUMP_FILE" "$DB_NAME"
    if [[ $? -ne 0 ]]; then
        echo "Error dumping database $DB_NAME"
        return 1
    fi
    echo "Backup completed for $DB_NAME"
}

fix_ownership() {
    local DB_NAME=$1
    local PROD_ROLE="${DB_NAME}_production"
    PROD_ROLE="${PROD_ROLE//-/_}"
    echo "Fixing ownership for database ${DB_NAME}-production to role ${PROD_ROLE}..."
    psql -h "$DEST_HOST" -p "$DEST_PORT" -U "$DEST_USER" -d "${DB_NAME}-production" << EOF
    DO \$\$
    DECLARE
        r record;
    BEGIN
        -- Fix table ownership
        FOR r IN SELECT tablename FROM pg_tables WHERE schemaname = 'public'
        LOOP
            EXECUTE 'ALTER TABLE public.' || quote_ident(r.tablename) || ' OWNER TO ${PROD_ROLE}';
        END LOOP;
        
        -- Fix sequence ownership
        FOR r IN SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public'
        LOOP
            EXECUTE 'ALTER SEQUENCE public.' || quote_ident(r.sequence_name) || ' OWNER TO ${PROD_ROLE}';
        END LOOP;

        -- Fix view ownership
        FOR r IN SELECT viewname FROM pg_views WHERE schemaname = 'public'
        LOOP
            EXECUTE 'ALTER VIEW public.' || quote_ident(r.viewname) || ' OWNER TO ${PROD_ROLE}';
        END LOOP;

        -- Fix materialized view ownership
        FOR r IN SELECT matviewname FROM pg_matviews WHERE schemaname = 'public'
        LOOP
            EXECUTE 'ALTER MATERIALIZED VIEW public.' || quote_ident(r.matviewname) || ' OWNER TO ${PROD_ROLE}';
        END LOOP;

        -- Fix function ownership
        FOR r IN SELECT p.proname, pg_get_function_identity_arguments(p.oid) AS args
        FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        LOOP
            EXECUTE 'ALTER FUNCTION public.' || quote_ident(r.proname) || '(' || r.args || ') OWNER TO ${PROD_ROLE}';
        END LOOP;
    END \$\$;
EOF
}

restore_database() {
    local DB_NAME=$1
    local DUMP_FILE="$DUMP_DIR/$DB_NAME.sql"
    if [[ ! -f "$DUMP_FILE" ]]; then
        echo "Error: Backup file $DUMP_FILE not found"
        return 1
    fi
    echo "Restoring $DB_NAME to $DB_NAME-production on destination..."
    export PGPASSWORD=$DEST_PASS
    pg_restore -h "$DEST_HOST" -p "$DEST_PORT" -U "$DEST_USER" -d "$DB_NAME-production" --no-owner -c -v "$DUMP_FILE"
    # if [[ $? -ne 0 ]]; then
    #     echo "Error restoring database $DB_NAME"
    #     return 1
    # fi
    fix_ownership "$DB_NAME"
}

process_database() {
    local DB_NAME=$1
    case "$OPERATION" in
        "backup")
            backup_database "$DB_NAME"
            ;;
        "restore")
            restore_database "$DB_NAME"
            ;;
        *)
            echo "Error: Invalid operation. Use 'backup' or 'restore'"
            exit 1
            ;;
    esac
}

# Validate operation
if [[ -z "$OPERATION" ]]; then
    echo "Error: Operation (-o) must be specified as 'backup' or 'restore'"
    exit 1
fi

# Setup SSH tunnel only for backup operations and non-localhost
if [[ "$OPERATION" == "backup" && -n "$SSH_USER" && "$SOURCE_HOST" != "localhost" && "$SOURCE_HOST" != "127.0.0.1" ]]; then
    echo "Setting up SSH tunnel for backup from $SOURCE_HOST..."
    ssh -f -N -L "$LOCAL_PORT:localhost:$SOURCE_PORT" "$SSH_USER@$SOURCE_HOST"
    if [[ $? -ne 0 ]]; then
        echo "Failed to create SSH tunnel"
        exit 1
    fi
    # Update source port to use tunnel
    SOURCE_PORT=$LOCAL_PORT
    SOURCE_HOST="localhost"
    # Setup tunnel cleanup on script exit
    trap 'pkill -f "ssh.*$LOCAL_PORT:localhost:.*$SSH_USER@$SOURCE_HOST"' EXIT
fi

# Export passwords to avoid interactive prompts
export PGPASSWORD=$SOURCE_PASS

# Ensure dump directory exists
mkdir -p "$DUMP_DIR"

# Process either single database or list from file
if [[ -n "$SINGLE_DB" ]]; then
    process_database "$SINGLE_DB"
else
    if [[ ! -f "$DB_LIST_FILE" ]]; then
        echo "Error: $DB_LIST_FILE not found and no database specified with -n"
        exit 1
    fi
    
    while IFS= read -r DB_NAME; do
        if [[ -n "$DB_NAME" ]]; then
            process_database "$DB_NAME"
        fi
    done < "$DB_LIST_FILE"
fi

# Cleanup
unset PGPASSWORD
echo "All done!"
